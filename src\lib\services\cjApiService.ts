/**
 * CJ Affiliate API Service
 * 
 * This service integrates with Commission Junction's API to fetch prices from
 * affiliate partners like Fanatical, Humble Bundle, GamesPlanet, and others.
 * 
 * Note: CJ API endpoints are currently under investigation. This implementation
 * provides a framework that can be easily updated once correct endpoints are confirmed.
 */

interface CJGamePrice {
  store_name: string;
  price: number;
  original_price?: number;
  discount_percentage?: number;
  store_url: string;
  affiliate_url: string;
  currency: string;
  availability: 'available' | 'out_of_stock' | 'pre_order';
  region: string;
  advertiser_name: string;
  product_name: string;
}

interface CJSearchResult {
  products: Array<{
    'advertiser-name': string;
    'buy-url': string;
    'catalog-id': string;
    'currency': string;
    'description': string;
    'image-url': string;
    'in-stock': 'true' | 'false';
    'name': string;
    'price': string;
    'retail-price': string;
    'sale-price': string;
    'sku': string;
    'upc': string;
  }>;
}

export class CJApiService {
  private developerKey: string;
  private websiteId: string;
  private companyId: string;
  private baseUrl: string;
  private requestTimeout: number;
  private debugMode: boolean;

  constructor() {
    this.developerKey = process.env.CJ_DEVELOPER_KEY || '';
    this.websiteId = process.env.CJ_WEBSITE_ID || '';
    this.companyId = process.env.CJ_COMPANY_ID || '';
    this.baseUrl = process.env.CJ_API_BASE_URL || 'https://api.cj.com';
    this.requestTimeout = parseInt(process.env.CJ_REQUEST_TIMEOUT || '30000');
    this.debugMode = process.env.NODE_ENV === 'development';
  }

  /**
   * Check if CJ API is properly configured
   */
  isConfigured(): boolean {
    const isValid = !!(this.developerKey && this.websiteId && this.companyId);
    
    if (!isValid && this.debugMode) {
      console.log('❌ CJ API não configurado:', {
        hasDeveloperKey: !!this.developerKey,
        hasWebsiteId: !!this.websiteId,
        hasCompanyId: !!this.companyId
      });
    }
    
    return isValid;
  }

  /**
   * Get region-specific currency mapping
   */
  private getRegionCurrency(regionCode: string): string {
    const regionToCurrency: Record<string, string> = {
      'us': 'USD',
      'gb': 'GBP',
      'de': 'EUR',
      'fr': 'EUR',
      'it': 'EUR',
      'es': 'EUR',
      'br': 'BRL',
      'ca': 'CAD',
      'au': 'AUD',
      'jp': 'JPY',
      'kr': 'KRW',
      'global': 'USD'
    };
    return regionToCurrency[regionCode] || 'USD';
  }

  /**
   * Generate a consistent hash from game name for realistic mock pricing
   */
  private generateGameHash(gameName: string): number {
    let hash = 0;
    for (let i = 0; i < gameName.length; i++) {
      const char = gameName.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Generate realistic pricing based on currency and game hash
   */
  private generateRealisticPrice(gameHash: number, currency: string): number {
    const basePrices: Record<string, number[]> = {
      'USD': [9.99, 19.99, 29.99, 39.99, 49.99, 59.99],
      'EUR': [9.99, 19.99, 29.99, 39.99, 49.99, 59.99],
      'GBP': [7.99, 15.99, 24.99, 32.99, 39.99, 49.99],
      'BRL': [29.99, 49.99, 79.99, 99.99, 149.99, 199.99],
      'CAD': [12.99, 24.99, 39.99, 52.99, 64.99, 79.99],
      'AUD': [14.99, 29.99, 44.99, 59.99, 74.99, 89.99],
      'JPY': [1200, 2400, 3600, 4800, 6000, 7200],
      'KRW': [12000, 24000, 36000, 48000, 60000, 72000]
    };

    const prices = basePrices[currency] || basePrices['USD'];
    return prices[gameHash % prices.length];
  }

  /**
   * Generate affiliate tracking URL for CJ
   * In development mode, returns direct store URL for easier testing
   */
  private generateAffiliateUrl(buyUrl: string, advertiserName: string): string {
    try {
      // In development mode, use direct store URLs for easier testing
      if (this.debugMode) {
        console.log(`🔗 DEV MODE: Using direct store URL instead of affiliate link for ${advertiserName}`);
        return buyUrl;
      }

      // Production: CJ tracking URL format - multiple patterns to try
      const trackingPatterns = [
        `https://www.jdoqocy.com/click-${this.websiteId}-${Date.now()}?url=${encodeURIComponent(buyUrl)}`,
        `https://www.anrdoezrs.net/click-${this.websiteId}-${Date.now()}?url=${encodeURIComponent(buyUrl)}`,
        `https://www.dpbolvw.net/click-${this.websiteId}-${Date.now()}?url=${encodeURIComponent(buyUrl)}`
      ];

      // Use first pattern as default
      return trackingPatterns[0];
    } catch (error) {
      console.error('❌ Error generating affiliate URL:', error);
      return buyUrl; // Fallback to original URL
    }
  }

  /**
   * Mock implementation for testing - simulates Fanatical/CJ responses
   * This allows us to test the integration while API endpoints are being investigated
   */
  private getMockFanaticalData(gameName: string, regionCode: string): CJGamePrice[] {
    if (!this.debugMode) {
      return []; // Only return mock data in development
    }

    const currency = this.getRegionCurrency(regionCode);

    // Generate more realistic pricing based on game name hash for consistency
    const gameHash = this.generateGameHash(gameName);
    const basePrice = this.generateRealisticPrice(gameHash, currency);
    const discountPrice = basePrice * (0.6 + (gameHash % 30) / 100); // 60-90% of original price
    const discountPercentage = Math.round(((basePrice - discountPrice) / basePrice) * 100);

    const storeUrl = `https://www.fanatical.com/en/search?search=${encodeURIComponent(gameName)}`;
    const affiliateUrl = this.generateAffiliateUrl(storeUrl, 'Fanatical');

    console.log(`🧪 MOCK FANATICAL: Generated price for "${gameName}": ${currency} ${discountPrice.toFixed(2)} (${discountPercentage}% off ${basePrice.toFixed(2)})`);
    console.log(`🔗 MOCK FANATICAL: Store URL: ${storeUrl}`);
    console.log(`🎯 MOCK FANATICAL: ${this.debugMode ? 'Direct' : 'Affiliate'} URL: ${affiliateUrl}`);

    return [
      {
        store_name: 'Fanatical',
        price: Math.round(discountPrice * 100) / 100, // Round to 2 decimals
        original_price: Math.round(basePrice * 100) / 100,
        discount_percentage: discountPercentage,
        store_url: storeUrl,
        affiliate_url: affiliateUrl,
        currency: currency,
        availability: 'available',
        region: regionCode,
        advertiser_name: 'Fanatical.com',
        product_name: `${gameName} (Mock)`
      }
    ];
  }

  /**
   * Test CJ API connection with multiple endpoint attempts
   */
  async testConnection(): Promise<{ success: boolean; endpoint?: string; message: string }> {
    if (!this.isConfigured()) {
      return { success: false, message: 'CJ API não configurado - verifique as credenciais' };
    }

    // Endpoints to test based on our research
    const testEndpoints = [
      `${this.baseUrl}/v3/advertiser-lookup`,
      `${this.baseUrl}/v2/advertiser-lookup`,
      'https://commission-detail.api.cj.com/v3/commissions',
      'https://commission-detail.api.cj.com/v2/commissions'
    ];

    for (const endpoint of testEndpoints) {
      try {
        console.log(`🔍 Testing CJ endpoint: ${endpoint}`);
        
        const response = await fetch(`${endpoint}?keywords=test&records-per-page=1`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.developerKey}`,
            'Accept': 'application/xml',
            'User-Agent': 'CriticalPixel/1.0'
          },
          signal: AbortSignal.timeout(this.requestTimeout)
        });

        console.log(`📊 Response: ${response.status} ${response.statusText}`);

        if (response.status === 200) {
          const text = await response.text();
          if (text.includes('<?xml') || text.includes('{')) {
            return { 
              success: true, 
              endpoint, 
              message: `Conectado com sucesso ao endpoint: ${endpoint}` 
            };
          }
        } else if (response.status === 400) {
          // Bad request might mean the endpoint exists but parameters are wrong
          const errorText = await response.text();
          if (errorText.includes('Invalid Key') || errorText.includes('keywords')) {
            return { 
              success: true, 
              endpoint, 
              message: `Endpoint funcional encontrado (erro de parâmetros): ${endpoint}` 
            };
          }
        }
             } catch (error) {
         console.log(`❌ Endpoint ${endpoint} failed:`, error instanceof Error ? error.message : String(error));
      }
    }

    return { 
      success: false, 
      message: 'Nenhum endpoint CJ funcional encontrado. Usando dados mock para desenvolvimento.' 
    };
  }

  /**
   * Search for games in CJ affiliate network
   * Currently returns mock data while endpoints are being investigated
   */
  async searchGames(gameName: string, regionCode: string = 'global'): Promise<CJSearchResult | null> {
    if (!this.isConfigured()) {
      console.error('❌ CJ API não está configurado');
      return null;
    }

    try {
      // First test if we have a working endpoint
      const connectionTest = await this.testConnection();
      
      if (connectionTest.success && connectionTest.endpoint) {
        // TODO: Implement actual API call once correct endpoint is confirmed
        console.log(`✅ ${connectionTest.message}`);
        console.log(`🚧 API real será implementada quando endpoint for confirmado`);
      }

      // For now, return mock data in development
      if (this.debugMode) {
        console.log(`🧪 Retornando dados mock para "${gameName}" em região ${regionCode}`);
        
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        
        return {
          products: [
            {
              'advertiser-name': 'Fanatical.com',
              'buy-url': `https://www.fanatical.com/en/search?search=${encodeURIComponent(gameName)}`,
              'catalog-id': 'mock-catalog-id',
              'currency': this.getRegionCurrency(regionCode),
              'description': `${gameName} - Digital PC Game Key`,
              'image-url': 'https://via.placeholder.com/150x200?text=Game',
              'in-stock': 'true',
              'name': `${gameName} (Mock)`,
              'price': '29.99',
              'retail-price': '39.99',
              'sale-price': '19.99',
              'sku': 'mock-sku-123',
              'upc': 'mock-upc-456'
            }
          ]
        };
      }

      console.log(`⚠️ CJ API em modo produção - dados reais não disponíveis ainda`);
      return null;

    } catch (error) {
      console.error('❌ CJ API Search Error:', error);
      return null;
    }
  }

  /**
   * Get game price specifically from Fanatical via CJ
   */
  async getFanaticalPrice(gameName: string, regionCode: string = 'global'): Promise<CJGamePrice | null> {
    try {
      // In development, return mock data
      if (this.debugMode) {
        const mockData = this.getMockFanaticalData(gameName, regionCode);
        if (mockData.length > 0) {
          console.log(`🧪 Mock Fanatical price: ${mockData[0].currency} ${mockData[0].price} para "${gameName}"`);
          return mockData[0];
        }
      }

      const searchResult = await this.searchGames(gameName, regionCode);
      
      if (!searchResult || !searchResult.products) {
        return null;
      }

      // Filter for Fanatical products
      const fanaticalProducts = searchResult.products.filter(product => 
        product['advertiser-name'].toLowerCase().includes('fanatical')
      );

      if (fanaticalProducts.length === 0) {
        console.log(`⚠️ CJ API: Nenhum produto Fanatical encontrado para "${gameName}"`);
        return null;
      }

      // Get the best match (first result)
      const product = fanaticalProducts[0];
      
      // Parse pricing
      const salePrice = parseFloat(product['sale-price'] || product['price'] || '0');
      const retailPrice = parseFloat(product['retail-price'] || '0');
      
      if (salePrice === 0) {
        console.log(`⚠️ CJ API: Preço inválido para produto Fanatical "${product.name}"`);
        return null;
      }

      // Calculate discount
      let discountPercentage = 0;
      if (retailPrice > salePrice && retailPrice > 0) {
        discountPercentage = Math.round(((retailPrice - salePrice) / retailPrice) * 100);
      }

      // Generate affiliate URL
      const affiliateUrl = this.generateAffiliateUrl(product['buy-url'], product['advertiser-name']);

      const gamePrice: CJGamePrice = {
        store_name: 'Fanatical',
        price: salePrice,
        original_price: retailPrice > salePrice ? retailPrice : undefined,
        discount_percentage: discountPercentage > 0 ? discountPercentage : undefined,
        store_url: product['buy-url'],
        affiliate_url: affiliateUrl,
        currency: product.currency || this.getRegionCurrency(regionCode),
        availability: product['in-stock'] === 'true' ? 'available' : 'out_of_stock',
        region: regionCode,
        advertiser_name: product['advertiser-name'],
        product_name: product.name
      };

      console.log(`💰 CJ/Fanatical: Preço encontrado ${gamePrice.currency} ${gamePrice.price} para "${gameName}"`);
      
      return gamePrice;

    } catch (error) {
      console.error('❌ CJ Fanatical Price Error:', error);
      return null;
    }
  }

  /**
   * Get game prices from all CJ gaming affiliates
   */
  async getAllGamingPrices(gameName: string, regionCode: string = 'global'): Promise<CJGamePrice[]> {
    try {
      // In development, return mock data for multiple stores
      if (this.debugMode) {
        const currency = this.getRegionCurrency(regionCode);
        const gameHash = this.generateGameHash(gameName);

        const mockStores = [
          {
            name: 'Fanatical',
            baseUrl: 'https://www.fanatical.com/en/search?search=',
            priceMultiplier: 0.75 // Usually cheapest
          },
          {
            name: 'Humble Bundle',
            baseUrl: 'https://www.humblebundle.com/store/search?search=',
            priceMultiplier: 0.85 // Mid-range pricing
          },
          {
            name: 'GamesPlanet',
            baseUrl: 'https://www.gamesplanet.com/search?query=',
            priceMultiplier: 0.80 // Good deals
          }
        ];

        console.log(`🌍 MOCK CJ STORES: Generating prices for "${gameName}" in ${currency} (${regionCode})`);

        return mockStores.map((store, index) => {
          const basePrice = this.generateRealisticPrice(gameHash + index, currency);
          const discountPrice = Math.round(basePrice * store.priceMultiplier * 100) / 100;
          const discountPercentage = Math.round(((basePrice - discountPrice) / basePrice) * 100);

          const storeUrl = `${store.baseUrl}${encodeURIComponent(gameName)}`;
          const affiliateUrl = this.generateAffiliateUrl(storeUrl, store.name);

          console.log(`💰 MOCK ${store.name}: ${currency} ${discountPrice} (${discountPercentage}% off ${basePrice})`);
          console.log(`🔗 MOCK ${store.name}: ${this.debugMode ? 'Direct' : 'Affiliate'} URL: ${affiliateUrl}`);

          return {
            store_name: store.name,
            price: discountPrice,
            original_price: basePrice,
            discount_percentage: discountPercentage,
            store_url: storeUrl,
            affiliate_url: affiliateUrl,
            currency: currency,
            availability: 'available' as const,
            region: regionCode,
            advertiser_name: `${store.name}.com`,
            product_name: `${gameName} (Mock)`
          };
        });
      }

      const searchResult = await this.searchGames(gameName, regionCode);
      
      if (!searchResult || !searchResult.products) {
        return [];
      }

      const gamingAdvertisers = [
        'fanatical',
        'humble',
        'gamesplanet', 
        'greenmangaming',
        'cdkeys',
        'voidu',
        'eneba'
      ];

      const gamingProducts = searchResult.products.filter(product => 
        gamingAdvertisers.some(advertiser => 
          product['advertiser-name'].toLowerCase().includes(advertiser)
        )
      );

      const prices: CJGamePrice[] = [];

      for (const product of gamingProducts) {
        const salePrice = parseFloat(product['sale-price'] || product['price'] || '0');
        const retailPrice = parseFloat(product['retail-price'] || '0');
        
        if (salePrice === 0) continue;

        // Calculate discount
        let discountPercentage = 0;
        if (retailPrice > salePrice && retailPrice > 0) {
          discountPercentage = Math.round(((retailPrice - salePrice) / retailPrice) * 100);
        }

        // Generate affiliate URL
        const affiliateUrl = this.generateAffiliateUrl(product['buy-url'], product['advertiser-name']);

        // Clean store name
        const storeName = this.cleanStoreName(product['advertiser-name']);

        const gamePrice: CJGamePrice = {
          store_name: storeName,
          price: salePrice,
          original_price: retailPrice > salePrice ? retailPrice : undefined,
          discount_percentage: discountPercentage > 0 ? discountPercentage : undefined,
          store_url: product['buy-url'],
          affiliate_url: affiliateUrl,
          currency: product.currency || this.getRegionCurrency(regionCode),
          availability: product['in-stock'] === 'true' ? 'available' : 'out_of_stock',
          region: regionCode,
          advertiser_name: product['advertiser-name'],
          product_name: product.name
        };

        prices.push(gamePrice);
        console.log(`💰 CJ/${storeName}: Preço encontrado ${gamePrice.currency} ${gamePrice.price}`);
      }

      console.log(`✅ CJ API: ${prices.length} preços de gaming encontrados para "${gameName}"`);
      return prices;

    } catch (error) {
      console.error('❌ CJ All Gaming Prices Error:', error);
      return [];
    }
  }

  /**
   * Clean and normalize store names
   */
  private cleanStoreName(advertiserName: string): string {
    const nameMapping: Record<string, string> = {
      'Fanatical.com': 'Fanatical',
      'Humble Bundle': 'Humble Bundle',
      'GamesPlanet': 'GamesPlanet',
      'GreenManGaming': 'Green Man Gaming',
      'CDKeys': 'CDKeys',
      'Voidu': 'Voidu',
      'Eneba': 'Eneba'
    };

    // Check exact matches first
    if (nameMapping[advertiserName]) {
      return nameMapping[advertiserName];
    }

    // Check partial matches
    for (const [key, value] of Object.entries(nameMapping)) {
      if (advertiserName.toLowerCase().includes(key.toLowerCase())) {
        return value;
      }
    }

    // Fallback: clean the original name
    return advertiserName
      .replace(/\.com$/i, '')
      .replace(/[^\w\s]/g, '')
      .trim();
  }

  /**
   * Format price with currency symbol
   */
  formatPrice(price: number, currency: string): string {
    try {
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(price);
    } catch (error) {
      return `${currency} ${price.toFixed(2)}`;
    }
  }
}

// Export singleton instance
export const cjApiService = new CJApiService(); 