require('dotenv').config({ path: '.env.local' });

async function testCJFanatical() {
    console.log('🧪 Testing CJ/Fanatical integration...\n');
    
    try {
        // Import dinamically to avoid module issues
        const { cjApiService } = await import('./src/lib/services/cjApiService.ts');
        
        console.log('📋 Testing configuration...');
        if (!cjApiService.isConfigured()) {
            console.error('❌ CJ API not configured. Check your .env.local file');
            return;
        }
        
        console.log('✅ CJ API configured correctly\n');
        
        // Test searching for a popular game
        const testGames = [
            'Call of Duty',
            'Cyberpunk 2077',
            'Assassins Creed',
            'Grand Theft Auto'
        ];
        
        for (const gameName of testGames) {
            console.log(`🎮 Testing search for: "${gameName}"`);
            
            // Test Fanatical specifically
            console.log('  📍 Searching Fanatical via CJ...');
            const fanaticalPrice = await cjApiService.getFanaticalPrice(gameName, 'us');
            
            if (fanaticalPrice) {
                console.log(`  ✅ Fanatical: ${fanaticalPrice.currency} ${fanaticalPrice.price}`);
                if (fanaticalPrice.discount_percentage) {
                    console.log(`     💸 Discount: ${fanaticalPrice.discount_percentage}%`);
                }
                console.log(`     🔗 Store: ${fanaticalPrice.store_url}`);
                console.log(`     🎯 Affiliate: ${fanaticalPrice.affiliate_url}`);
            } else {
                console.log('  ⚠️ No Fanatical price found');
            }
            
            // Test all CJ gaming stores
            console.log('  🌍 Searching all CJ gaming stores...');
            const allPrices = await cjApiService.getAllGamingPrices(gameName, 'us');
            
            if (allPrices && allPrices.length > 0) {
                console.log(`  ✅ Found prices in ${allPrices.length} CJ stores:`);
                allPrices.forEach(price => {
                    console.log(`     💰 ${price.store_name}: ${price.currency} ${price.price}`);
                });
            } else {
                console.log('  ⚠️ No CJ gaming store prices found');
            }
            
            console.log(); // Add spacing
        }
        
        console.log('🎯 CJ/Fanatical test completed!');
        
    } catch (error) {
        console.error('💥 Test failed:', error);
        console.error('Stack:', error.stack);
    }
}

// Run the test
testCJFanatical(); 