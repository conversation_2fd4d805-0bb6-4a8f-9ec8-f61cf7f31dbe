# 🔑 Guia de Configuração da API CJ (Commission Junction)

> **📖 DOCUMENTAÇÃO COMPLETA DISPONÍVEL:**
> **[cjDocumentaion.md](./cjDocumentaion.md)** - G<PERSON><PERSON> completo e atualizado para implementação CJ API

## ✅ Status: CONFIGURAÇÃO COMPLETA

**As variáveis de ambiente já estão 100% configuradas e funcionando!**

```bash
CJ_WEBSITE_ID=*********                    # ✅ Configurado
CJ_DEVELOPER_KEY=xfFMjkz-yyuNksix_dWFMnkgcA  # ✅ Configurado
CJ_COMPANY_ID=7623097                      # ✅ Configurado
```

## 🚨 **PRÓXIMO PASSO CRÍTICO: Programas de Afiliados**

Para que a API retorne dados reais (não mock), você deve aplicar e ser aprovado nos programas de afiliados das lojas gaming:

- 🔴 **Fanatical**: [Aplicar Agora](https://www.cj.com) → Find Advertisers → "Fanatical"
- 🔴 **Humble Bundle**: [Aplicar Agora](https://www.cj.com) → Find Advertisers → "Humble Bundle"
- 🔴 **GamesPlanet**: [Aplicar Agora](https://www.cj.com) → Find Advertisers → "GamesPlanet"

## Pré-requisitos Completados ✅

Agora que você já seguiu os passos anteriores para obter suas credenciais da CJ, vamos configurar as variáveis de ambiente no seu projeto.

## Configuração das Variáveis de Ambiente

### 1. Editar o arquivo .env.local

Abra o arquivo `.env.local` na raiz do seu projeto e adicione as seguintes variáveis:

```bash
# CJ Affiliate API Configuration
# Substitua os valores pelos dados reais da sua conta CJ

# Website ID (PID) - Encontrado em Account > Websites
CJ_WEBSITE_ID=1234567

# Personal Access Token - Obtido de developers.cj.com
CJ_DEVELOPER_KEY=your_personal_access_token_here

# Company ID (CID) - Encontrado no canto superior direito da sua conta
CJ_COMPANY_ID=your_company_id_here

# CJ API Base URL (não alterar)
CJ_API_BASE_URL=https://api.cj.com

# CJ API Version
CJ_API_VERSION=v3

# Configurações opcionais
CJ_CURRENCY=USD
CJ_REQUEST_TIMEOUT=30000

# Ambiente de desenvolvimento
NODE_ENV=development
```

### 2. Onde Encontrar Cada Credencial

#### CJ_WEBSITE_ID (PID)
1. Login na sua conta CJ
2. Vá para **Account > Websites**
3. Localize o **PID** da sua propriedade

#### CJ_DEVELOPER_KEY (Personal Access Token)
1. Acesse [developers.cj.com](https://developers.cj.com)
2. Vá para **Authentication and Personal Access Tokens**
3. Clique em **Create a new Personal Access Token**
4. Nomeie como "CriticalPixel-API"
5. Copie o token gerado

#### CJ_COMPANY_ID (CID)
1. Na sua conta CJ, localize o **CID** no canto superior direito
2. Está próximo ao seu nome de usuário

### 3. Exemplo de Configuração Real

```bash
# Exemplo com dados fictícios
CJ_WEBSITE_ID=8765432
CJ_DEVELOPER_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IjdkRC1nZWNOZ1gxWmY3R0xrT3ZwT0IyZGNWQSIsImtpZCI6IjdkRC1nZWNOZ1gxWmY3R0xrT3ZwT0IyZGNWQSJ9
CJ_COMPANY_ID=4567890
CJ_API_BASE_URL=https://api.cj.com
CJ_API_VERSION=v3
CJ_CURRENCY=USD
CJ_REQUEST_TIMEOUT=30000
```

## 🔐 Segurança Importante

- **NUNCA** commite o arquivo `.env.local` no Git
- O `.env.local` já está no `.gitignore` por padrão
- Mantenha suas credenciais privadas e seguras
- Considere usar diferentes tokens para desenvolvimento e produção

## ✅ Verificação da Configuração

Para testar se as variáveis estão configuradas corretamente, execute este comando no terminal:

```bash
node -e "console.log('CJ_WEBSITE_ID:', process.env.CJ_WEBSITE_ID)"
```

## 📋 Próximos Passos

Depois de configurar as variáveis de ambiente:

1. ✅ Configuração das variáveis (atual)
2. 🔄 Implementação do serviço CJ API
3. 🔄 Integração no sistema de preços
4. 🔄 Testes da API
5. 🔄 Deploy em produção

## 🆘 Problemas Comuns

### Token Inválido
- Verifique se copiou o token completo
- Certifique-se de que o token não expirou (24h para development tokens)

### Website ID não encontrado
- Verifique se você está logado na conta correta
- Confirme se sua conta foi aprovada como publisher

### Erro de CORS
- A API da CJ deve ser chamada do backend (Next.js API routes)
- Nunca exponha as credenciais no frontend

## 📚 Documentação Oficial

- [CJ Developer Portal](https://developers.cj.com)
- [CJ API Documentation](https://developers.cj.com/docs)
- [Authentication Guide](https://developers.cj.com/docs/authentication) 