# 🎯 CJ Affiliate API - Complete Implementation & Debugging Guide

## 📋 **Table of Contents**
1. [Overview & Requirements](#overview--requirements)
2. [API Setup & Authentication](#api-setup--authentication)
3. [Advertiser Program Requirements](#advertiser-program-requirements)
4. [API Endpoints & Implementation](#api-endpoints--implementation)
5. [Adding New Stores](#adding-new-stores)
6. [Debugging & Troubleshooting](#debugging--troubleshooting)
7. [Development vs Production](#development-vs-production)
8. [Code Examples](#code-examples)

---

## 🎯 **Overview & Requirements**

### **What is CJ Affiliate API?**
Commission Junction (CJ) Affiliate API allows you to fetch real-time product prices and generate affiliate tracking links from partner stores like Fanatical, Humble Bundle, GamesPlanet, etc.

### **Critical Requirements:**
1. **✅ CJ Affiliate Account** - Must be approved as a publisher
2. **✅ API Credentials** - Developer key, Website ID, Company ID
3. **✅ Advertiser Relationships** - Must be accepted into each store's affiliate program
4. **✅ Active Partnerships** - Each store must approve your application individually

### **Why API Returns Empty Results:**
- **Most Common Issue**: Not accepted into advertiser programs
- **Second Most Common**: Incorrect API endpoints or credentials
- **Third Most Common**: API rate limiting or authentication errors

---

## 🔑 **API Setup & Authentication**

### **Required Environment Variables:**
```bash
# CJ Affiliate API Configuration
CJ_WEBSITE_ID=*********                    # Your Website/Property ID (PID)
CJ_DEVELOPER_KEY=xfFMjkz-yyuNksix_dWFMnkgcA  # Personal Access Token
CJ_COMPANY_ID=7623097                      # Your Company ID (CID)
CJ_API_BASE_URL=https://api.cj.com         # Base API URL
CJ_API_VERSION=v3                          # API Version
CJ_CURRENCY=USD                            # Default currency
CJ_REQUEST_TIMEOUT=30000                   # Request timeout in ms
```

### **Where to Find Credentials:**

#### **CJ_WEBSITE_ID (PID):**
1. Login to CJ Affiliate account
2. Go to **Account** → **Websites**
3. Find your website's **PID** number

#### **CJ_DEVELOPER_KEY:**
1. Visit [developers.cj.com](https://developers.cj.com)
2. Go to **Authentication** → **Personal Access Tokens**
3. Create new token named "CriticalPixel-API"
4. Copy the generated token

#### **CJ_COMPANY_ID (CID):**
1. In your CJ account, look at top-right corner
2. Find the **CID** number next to your username

---

## 🏪 **Advertiser Program Requirements**

### **CRITICAL: You Must Apply to Each Store Individually**

The CJ API will **ONLY** return products from stores you have an **active affiliate relationship** with.

### **Gaming Stores to Apply For:**

| Store | CJ Program Name | Approval Time | Commission Rate |
|-------|----------------|---------------|-----------------|
| **Fanatical** | Fanatical.com | 1-3 days | 3-8% |
| **Humble Bundle** | Humble Bundle | 1-7 days | 5-15% |
| **GamesPlanet** | GamesPlanet | 1-5 days | 4-10% |
| **Green Man Gaming** | Green Man Gaming | 2-7 days | 3-7% |
| **CDKeys** | CDKeys.com | 1-3 days | 2-5% |
| **Voidu** | Voidu.com | 1-5 days | 4-8% |

### **How to Apply:**
1. **Login to CJ Affiliate**: [cj.com](https://www.cj.com)
2. **Navigate**: Advertisers → Find Advertisers
3. **Search**: Enter store name (e.g., "Fanatical")
4. **Apply**: Click "Apply to Program"
5. **Wait**: Check email for approval notifications
6. **Verify**: Go to Account → Advertiser Relationships

### **Application Tips:**
- **Website Quality**: Ensure your site looks professional
- **Content Relevance**: Have gaming-related content
- **Traffic**: Some stores require minimum traffic
- **Compliance**: Follow each store's terms and conditions

---

## 🔌 **API Endpoints & Implementation**

### **Current Working Endpoints (2024):**

#### **1. Advertiser Lookup API:**
```
GET https://advertiser-lookup.api.cj.com/v3/advertiser-lookup
```
- **Purpose**: Check which advertisers you have access to
- **Authentication**: Bearer token in Authorization header

#### **2. Product Search API (GraphQL):**
```
POST https://product-search.api.cj.com/graphql
```
- **Purpose**: Search for products from your approved advertisers
- **Method**: GraphQL queries
- **Authentication**: Bearer token

#### **3. Link Search API:**
```
GET https://link-search.api.cj.com/v2/link-search
```
- **Purpose**: Generate affiliate tracking links
- **Parameters**: advertiser-ids, keywords, website-id

### **API Authentication Headers:**
```javascript
const headers = {
  'Authorization': `Bearer ${CJ_DEVELOPER_KEY}`,
  'Accept': 'application/json',
  'Content-Type': 'application/json'
};
```

### **GraphQL Product Search Query:**
```graphql
query ProductSearch($keywords: String!, $advertiserId: String) {
  productSearch(
    keywords: $keywords
    advertiserId: $advertiserId
    first: 20
  ) {
    edges {
      node {
        name
        price
        salePrice
        currency
        imageUrl
        productUrl
        advertiser {
          name
          id
        }
      }
    }
  }
}
```

---

## ➕ **Adding New Stores**

### **Step 1: Apply to Store's Affiliate Program**
1. Search for the store in CJ Affiliate platform
2. Apply to their program
3. Wait for approval (1-7 days typically)
4. Verify approval in "Advertiser Relationships"

### **Step 2: Add Store to Code**

#### **Update Store Configuration:**
```typescript
// src/lib/services/cjApiService.ts
const GAMING_STORES = {
  'Fanatical': {
    searchUrl: 'https://www.fanatical.com/en/search?search=',
    cjAdvertiserName: 'Fanatical.com',
    priceMultiplier: 0.75, // Usually cheapest
    commission: '3-8%'
  },
  'Humble Bundle': {
    searchUrl: 'https://www.humblebundle.com/store/search?search=',
    cjAdvertiserName: 'Humble Bundle',
    priceMultiplier: 0.85,
    commission: '5-15%'
  },
  'NEW_STORE_NAME': {
    searchUrl: 'https://www.newstore.com/search?q=',
    cjAdvertiserName: 'New Store Name in CJ',
    priceMultiplier: 0.80,
    commission: '4-10%'
  }
};
```

#### **Add Store Styling:**
```typescript
// src/components/game/GamePricesWidget.tsx
const storeColors: Record<string, string> = {
  // ... existing stores
  'NEW_STORE_NAME': 'text-purple-500' // Choose appropriate color
};

const storeGradients: Record<string, string> = {
  // ... existing stores
  'NEW_STORE_NAME': 'from-purple-700 to-purple-600'
};

const STORE_COLORS = {
  // ... existing stores
  'NEW_STORE_NAME': 'bg-purple-600'
};
```

### **Step 3: Test Integration**
1. Use the test API: `/api/test-cj-advertisers`
2. Verify the store appears in your approved advertisers
3. Test product search with store-specific keywords
4. Verify affiliate links generate correctly

### **Step 4: Update Mock Data (Development)**
```typescript
// Add to getMockFanaticalData or getAllGamingPrices
const mockStores = [
  // ... existing stores
  {
    name: 'NEW_STORE_NAME',
    baseUrl: 'https://www.newstore.com/search?q=',
    priceMultiplier: 0.80
  }
];
```

---

## 🐛 **Debugging & Troubleshooting**

### **Common Issues & Solutions:**

#### **1. Empty API Responses**
**Symptoms**: API returns `[]` or no products
**Causes**:
- Not approved for any advertiser programs
- Searching for products from non-approved advertisers
- Invalid search keywords

**Solutions**:
```bash
# Test which advertisers you have access to
curl -H "Authorization: Bearer YOUR_TOKEN" \
     https://advertiser-lookup.api.cj.com/v3/advertiser-lookup
```

#### **2. Authentication Errors**
**Symptoms**: 401 Unauthorized, 403 Forbidden
**Causes**:
- Invalid or expired developer key
- Incorrect website ID
- Missing authentication headers

**Solutions**:
- Regenerate Personal Access Token
- Verify website ID in CJ account
- Check environment variables

#### **3. API Endpoint Errors**
**Symptoms**: 404 Not Found, 500 Server Error
**Causes**:
- Using deprecated endpoints
- Incorrect API base URL
- API version mismatch

**Solutions**:
- Use current endpoints listed in this guide
- Check CJ Developer Portal for updates
- Test with Postman/curl first

### **Debug API Endpoints:**

#### **Test Advertiser Access:**
```typescript
// GET /api/test-cj-advertisers
// Returns list of advertisers you have access to
```

#### **Test Product Search:**
```typescript
// GET /api/debug-cj-products?keywords=game&advertiser=Fanatical
// Tests product search for specific advertiser
```

#### **Test Link Generation:**
```typescript
// GET /api/debug-cj-links?url=https://store.com/product
// Tests affiliate link generation
```

---

## 🔄 **Development vs Production**

### **Development Mode:**
- **Mock Data**: Returns realistic fake prices for testing
- **Direct URLs**: Uses store URLs directly (no affiliate tracking)
- **Logging**: Detailed console logs for debugging
- **Fast Testing**: No API rate limits or approval requirements

### **Production Mode:**
- **Real API**: Calls actual CJ API endpoints
- **Affiliate URLs**: Generates tracking links for commission
- **Error Handling**: Graceful fallbacks for API failures
- **Rate Limiting**: Respects CJ API rate limits

### **Mode Configuration:**
```typescript
// Automatically determined by NODE_ENV
const isDebugMode = process.env.NODE_ENV === 'development';

if (isDebugMode) {
  // Use mock data and direct URLs
  return getMockData(gameName, region);
} else {
  // Use real CJ API
  return await callCJAPI(gameName, region);
}
```

### **Environment Detection:**
```typescript
// src/lib/services/cjApiService.ts
class CJApiService {
  private debugMode: boolean;

  constructor() {
    this.debugMode = process.env.NODE_ENV === 'development';
    console.log(`🔧 CJ API Service initialized in ${this.debugMode ? 'DEVELOPMENT' : 'PRODUCTION'} mode`);
  }
}
```

---

## 💻 **Code Examples**

### **Basic Product Search:**
```typescript
async function searchCJProducts(keywords: string, advertiser?: string) {
  const headers = {
    'Authorization': `Bearer ${process.env.CJ_DEVELOPER_KEY}`,
    'Accept': 'application/json'
  };

  const params = new URLSearchParams({
    'website-id': process.env.CJ_WEBSITE_ID!,
    'keywords': keywords,
    ...(advertiser && { 'advertiser-name': advertiser })
  });

  const response = await fetch(
    `https://product-search.api.cj.com/v2/product-search?${params}`,
    { headers }
  );

  return await response.json();
}
```

### **GraphQL Product Search:**
```typescript
async function searchProductsGraphQL(keywords: string, advertiserId?: string) {
  const query = `
    query ProductSearch($keywords: String!, $advertiserId: String) {
      productSearch(
        keywords: $keywords
        advertiserId: $advertiserId
        first: 20
      ) {
        edges {
          node {
            name
            price
            salePrice
            currency
            productUrl
            advertiser { name id }
          }
        }
      }
    }
  `;

  const response = await fetch('https://product-search.api.cj.com/graphql', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.CJ_DEVELOPER_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      query,
      variables: { keywords, advertiserId }
    })
  });

  return await response.json();
}
```

### **Generate Affiliate Link:**
```typescript
function generateAffiliateUrl(storeUrl: string, advertiserId: string): string {
  const timestamp = Date.now();
  const websiteId = process.env.CJ_WEBSITE_ID;

  // Development mode: return direct URL
  if (process.env.NODE_ENV === 'development') {
    return storeUrl;
  }

  // Production mode: return CJ tracking URL
  return `https://www.jdoqocy.com/click-${websiteId}-${timestamp}?url=${encodeURIComponent(storeUrl)}`;
}
```

### **Error Handling:**
```typescript
async function fetchCJPrices(gameName: string) {
  try {
    const response = await searchCJProducts(gameName);

    if (!response.products || response.products.length === 0) {
      console.warn('No products found - check advertiser relationships');
      return [];
    }

    return response.products.map(formatProduct);
  } catch (error) {
    console.error('CJ API Error:', error);
    return []; // Graceful fallback
  }
}
```

### **Complete Integration Example:**
```typescript
// src/lib/services/cjApiService.ts
export class CJApiService {
  async getFanaticalPrice(gameName: string, region: string): Promise<CJGamePrice | null> {
    try {
      // Development mode: return mock data
      if (this.debugMode) {
        const mockData = this.getMockFanaticalData(gameName, region);
        return mockData[0] || null;
      }

      // Production mode: call real API
      const products = await this.searchProducts(gameName, 'Fanatical');

      if (products.length === 0) {
        console.warn('No Fanatical products found - check if approved for Fanatical program');
        return null;
      }

      return this.formatProduct(products[0], region);
    } catch (error) {
      console.error('Error fetching Fanatical price:', error);
      return null;
    }
  }
}
```

---

## 🎯 **Quick Start Checklist**

### **For New Store Integration:**
- [ ] Apply to store's affiliate program in CJ
- [ ] Wait for approval (check email + CJ account)
- [ ] Add store configuration to `cjApiService.ts`
- [ ] Add store styling to `GamePricesWidget.tsx`
- [ ] Test with `/api/test-cj-advertisers`
- [ ] Verify products appear in price widget
- [ ] Test affiliate links work correctly

### **For Debugging Issues:**
- [ ] Check environment variables are set
- [ ] Verify advertiser relationships in CJ account
- [ ] Test API endpoints with curl/Postman
- [ ] Check console logs for detailed errors
- [ ] Use debug API endpoints for testing
- [ ] Verify search keywords match store products

### **For API Implementation:**
- [ ] Implement real API calls (not just mock data)
- [ ] Add proper error handling and fallbacks
- [ ] Test with multiple stores and products
- [ ] Verify affiliate link generation
- [ ] Test rate limiting and timeout handling
- [ ] Add logging for debugging

---

## 🔧 **Debug Commands**

### **Test CJ API Access:**
```bash
# Test advertiser lookup
curl -H "Authorization: Bearer YOUR_TOKEN" \
     https://advertiser-lookup.api.cj.com/v3/advertiser-lookup

# Test product search
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://product-search.api.cj.com/v2/product-search?website-id=YOUR_WEBSITE_ID&keywords=game"
```

### **Test Environment Variables:**
```bash
# Check if variables are set
echo $CJ_WEBSITE_ID
echo $CJ_DEVELOPER_KEY
echo $CJ_COMPANY_ID
```

### **Test API Endpoints in Code:**
```typescript
// Test endpoints
GET /api/test-cj-advertisers          // Check approved advertisers
GET /api/debug-cj-products           // Test product search
GET /test-prices                     // Test full integration
```

---

## 📞 **Support Resources**

- **CJ Developer Portal**: [developers.cj.com](https://developers.cj.com)
- **CJ Support**: Through your CJ account dashboard
- **API Status**: Check CJ status page for outages
- **Community**: CJ Affiliate forums and Discord

---

## 📝 **Implementation Notes**

### **Current Status (January 2025):**
- ✅ **Environment Variables**: Configured and working
- ✅ **Mock Data**: Implemented for development testing
- ✅ **Store Styling**: Added for Fanatical and other stores
- 🚧 **Real API**: Requires advertiser program approvals
- 🚧 **Production Mode**: Waiting for store approvals

### **Next Steps:**
1. **Apply to gaming store affiliate programs**
2. **Wait for approvals (1-7 days each)**
3. **Test real API with approved advertisers**
4. **Switch from mock to real data**
5. **Monitor and optimize performance**

### **Known Issues:**
- **Empty API responses**: Usually means not approved for advertiser programs
- **Rate limiting**: CJ has API rate limits (check documentation)
- **Endpoint changes**: CJ occasionally updates API endpoints

---

**Last Updated**: January 2025
**API Version**: v3
**Status**: Active Implementation
**Mode**: Development (Mock Data) → Production (Real API)
```