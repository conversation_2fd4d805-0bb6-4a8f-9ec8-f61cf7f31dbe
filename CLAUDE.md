# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Essential Commands

### Development
```bash
npm run dev          # Start development server on port 9003 (hostname 0.0.0.0)
npm run dev:turbo    # Start with Turbo mode for faster builds
npm run build        # Build for production
npm run start        # Start production server
```

### Quality Assurance
```bash
npm run lint         # Run ESLint
npm run typecheck    # Run TypeScript type checking
npm run test         # Run Jest tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage report
npm run test:ci      # Run tests for CI/CD (no watch, with coverage)
```

### AI Development
```bash
npm run genkit:dev   # Start Genkit AI development server
npm run genkit:watch # Start Genkit with file watching
```

### Analysis
```bash
npm run analyze      # Analyze bundle size with webpack bundle analyzer
```

### Setup
```bash
npm run setup:admin  # Create admin user setup (script at scripts/setup-admin-user.js)
```

## Architecture Overview

### Database Architecture
This project uses **Supabase** as the primary database:

- **Supabase PostgreSQL**: Primary database for authentication, reviews, performance surveys, comments, and analytics
- **Row Level Security**: Database-level security policies for data protection
- **Real-time subscriptions**: Live data updates for dynamic user experiences

### Key Architecture Patterns

#### App Router Structure
- Uses Next.js 15.3.3 App Router with Server Components
- API routes in `src/app/api/`
- Page components in `src/app/[route]/`
- Layout components with nested routing

#### Component Organization
- `src/components/ui/`: Base UI components (Radix UI + Shadcn)
- `src/components/review-form/`: Multi-step review creation system with Lexical editor
- `src/components/dashboard/`: User dashboard with analytics and performance insights
- `src/components/layout/`: Navigation, footer, and layout components with global controls
- `src/components/admin/`: Admin panel components with security controls
- `src/components/userprofile/`: Profile management and customization
- `src/components/forum/`: Forum system with posts, threads, and voting
- `src/components/game/`: Game-specific components including hero banners and pricing widgets

#### AI Integration
- **Google Genkit 1.6.2** for AI flows and content generation
- **Gemini 2.0 Flash** model configuration in `src/ai/genkit.ts`
- AI-powered SEO optimization in `src/ai/flows/generate-seo-flow.ts`
- Community AI detection voting system with confidence scoring
- Automated content analysis and metadata generation

#### Rich Text Editor
- **Lexical 0.17.1** with custom plugins and themes
- Located in `src/components/review-form/lexical/`
- Supports code blocks, tables, markdown shortcuts, and custom toolbar

#### Cloud Storage Integration
- **Backblaze B2**: Primary cloud storage for user-uploaded images
- **Image Processing**: Sharp-based optimization and conversion
- **Service Layer**: `/src/lib/services/b2StorageService.ts`
- **API Routes**: `/src/app/api/b2/*` for upload, delete, and management
- **Premium Features**: Enhanced image upload in premium toolbar

### Database Implementation
- **IMPORTANT**: Project uses Supabase as the primary database
- Authentication handled through Supabase Auth with JWT tokens
- All new features should use Supabase patterns and Row Level Security policies
- Client access via `src/lib/supabase/client.ts`, server access via `src/lib/supabase/server.ts`
- Type definitions in `src/lib/supabase/types.ts` must match database schema

## Development Guidelines

### Environment Variables
The application requires several environment variables for full functionality:

#### Required for Core Features
- `NEXT_PUBLIC_SUPABASE_URL`: Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY`: Supabase service role key for server operations

#### Optional for Enhanced Features
- `CJ_DEVELOPER_KEY`: Commission Junction API developer key for affiliate pricing
- `CJ_WEBSITE_ID`: CJ website ID for affiliate tracking
- `CJ_COMPANY_ID`: CJ company identifier
- `STEAM_API_KEY`: Steam API key for game metadata and pricing
- `IGDB_CLIENT_ID`: IGDB API client ID for game database
- `IGDB_CLIENT_SECRET`: IGDB API client secret
- `STEAMGRIDDB_API_KEY`: SteamGridDB API key for game artwork

### TypeScript Configuration
- **Build errors ignored** in production (`ignoreBuildErrors: true`)
- Full type safety expected for new code
- Type definitions in `src/lib/types.ts` and `src/lib/supabase/types.ts`

### Performance Optimizations
- Lexical packages are code-split into separate chunk
- Virtual scrolling implemented for large datasets
- Bundle analyzer available via `npm run analyze`
- Image optimization with AVIF/WebP support

### Next.js Configuration
- Server actions enabled with 2MB body size limit
- Standalone output for containerized deployments
- Custom webpack configuration for Lexical compatibility
- Development CORS headers enabled

### Authentication Patterns
- **Supabase Auth**: Primary authentication system with JWT tokens
- Use `src/lib/supabase/client.ts` for client-side auth operations
- Server-side auth patterns in `src/lib/supabase/server.ts`
- Row Level Security policies enforce data access controls

## Key Technical Decisions

### Bundle Management
- Lexical packages transpiled and optimized
- Console logs removed in production
- Bundle analysis integrated into build process

### Development Server
- Runs on port 9003 with hostname 0.0.0.0 for network access
- Turbo mode available for faster development builds

### AI Content Generation
- Automated SEO metadata generation
- Content analysis and optimization scoring
- Google Genkit integration for AI flows

## Testing Strategy
- **Jest** with React Testing Library for unit and integration tests
- Test files located in `src/**/__tests__/**/*.test.{js,ts,tsx}` and `tests/**/*.test.{js,ts,tsx}`
- Coverage threshold: 80% for branches, functions, lines, and statements
- Test setup file at `tests/setup.ts` with jsdom environment
- Use `npm run test` for single test run, `npm run test:watch` for development
- Use `npm run test:coverage` for coverage reports, `npm run test:ci` for CI/CD
- Use `npm run typecheck` to verify TypeScript compilation
- Use `npm run lint` for code quality checks

## Code Structure Patterns

### Component Conventions
- All components use TypeScript with proper type definitions
- UI components in `src/components/ui/` follow Radix UI + Shadcn patterns
- Custom hooks in `src/hooks/` with consistent naming (use-*)
- Service functions in `src/lib/` with clear separation of concerns
- Admin-related code in `src/lib/admin/` with security validations
- Server Actions pattern for forms with proper authentication checks

### Database Patterns
- Use Supabase client from `src/lib/supabase/client.ts` for browser operations
- Use Supabase server from `src/lib/supabase/server.ts` for server-side operations
- All database operations should respect Row Level Security policies
- Type definitions in `src/lib/supabase/types.ts` match database schema

### State Management
- React Query for server state management with persistence
- React Context for authentication state in `src/contexts/auth-context.tsx`
- React Context for global currency state in `src/contexts/currency-context.tsx`
- Form state with React Hook Form and Zod validation
- Local state with useState/useReducer for component-specific data
- Auth context provides user state, admin status, and suspension checking
- Currency context provides global currency selection with localStorage persistence

### File Naming Conventions
- Pages: PascalCase (e.g., `ReviewPage.tsx`)
- Components: PascalCase (e.g., `ReviewCard.tsx`)
- Hooks: camelCase with 'use' prefix (e.g., `useUserReviews.ts`)
- Utilities: camelCase (e.g., `slugify.ts`, `dateUtils.ts`)
- API routes: lowercase with hyphens (e.g., `route.ts` in folders)

## Gaming Platform Integrations

### IGDB API Integration
- **Game Metadata**: Automatic game data fetching via `src/lib/igdb-api.ts`
- **API Endpoints**: Routes in `src/app/api/igdb/` for game search and details
- **Rate Limiting**: Built-in throttling for API compliance
- **Data Caching**: Game metadata cached in database for performance

### SteamGridDB Integration
- **Artwork Management**: Game covers, banners, and icons via `src/lib/steamgriddb-api.ts`
- **Gallery Components**: `src/components/steamgriddb/` for artwork display and selection
- **API Routes**: Multiple endpoints for different asset types (grids, heroes, icons, logos)
- **Search Integration**: Unified search across game databases

### Hardware Performance Tracking
- **Component Database**: CPU, GPU, and handheld device data in `src/lib/hardware-data.ts`
- **Performance Surveys**: User hardware tracking via `src/lib/services/performanceSurveyService.ts`
- **Search Components**: Hardware-specific search inputs in `src/components/ui/HardwareSearchInput.tsx`
- **Analytics Integration**: Performance data tied to review analytics

### Game Pricing System
- **Price Aggregation**: Multi-store price comparison via Steam API, scraping services, and affiliate networks
- **Regional Support**: 25+ regions with currency conversion in `src/lib/constants/steamRegions.ts`
- **Cache Management**: Smart caching with auto-refresh triggers for price updates
- **API Integration**: Price fetching endpoints in `src/app/api/games/[id]/prices/`
- **Widget Components**: 
  - `GamePricesWidget.tsx`: Full-featured sidebar widget with region selector
  - `GamePricesWidgetInteractive.tsx`: Compact hero banner version with store buttons
- **Store Integration**: Steam, Epic Games, GOG, Nuuvem, instant gaming stores, and CJ affiliate partners
- **Responsive Layout**: Hero banner integration with analytics chart side-by-side
- **Conditional Stats**: Dynamic grid layout (2 or 3 columns) based on performance survey availability

#### CJ Affiliate Integration
- **CJ API Service**: Commission Junction affiliate network integration in `src/lib/services/cjApiService.ts`
- **Affiliate Partners**: Fanatical, Humble Bundle, GamesPlanet, Green Man Gaming, CDKeys, Voidu, Eneba
- **Mock Development**: Complete mock data system for development when API endpoints are under investigation
- **Affiliate Tracking**: Automatic affiliate URL generation with fallback to direct store URLs in development
- **Test Endpoints**: API testing route at `/api/test-cj-advertisers` for debugging CJ connections
- **Revenue Integration**: Affiliate commission tracking for monetization features

### Global Currency Management
- **Currency Context**: Global currency state management in `src/contexts/currency-context.tsx`
- **Currency Selector**: Navbar component in `src/components/ui/CurrencySelector.tsx`
- **Persistent Selection**: localStorage-based currency preference storage
- **Dynamic Mapping**: Currency-to-region mapping for automatic price localization
- **Available Currencies**: 12 major currencies (USD, EUR, GBP, JPY, KRW, CNY, RUB, BRL, MXN, CAD, AUD, INR)
- **Real-time Updates**: Currency changes automatically update all price widgets

## User Profile System

### Dual Profile Architecture
- **UserProfile**: Basic profile structure in `src/lib/types.ts`
- **ExtendedUserProfile**: Enhanced profile with gaming connections in `src/types/user-content.ts`
- **Conversion Utilities**: Profile transformation patterns for legacy compatibility
- **Content Modules**: Configurable profile sections in `src/components/userprofile/`

### Profile Customization
- **Theme System**: Advanced theming with `src/lib/ThemeManager.ts`
- **Content Modules**: YouTube integration, reviews display, gaming connections
- **Banner Configuration**: Monetization and sponsor banner management
- **Privacy Controls**: Granular privacy settings via `src/lib/services/privacyService.ts`

## Forum System Architecture

### Discussion System
- **Forum Posts**: Review-based discussions with voting and categorization
- **Threaded Replies**: Nested comment system with upvote/downvote functionality
- **User Interactions**: Comprehensive voting system with real-time updates
- **Content Management**: Post creation, editing, and moderation capabilities

### Forum Components
- **ForumSystem**: Main forum interface with post listings and filtering
- **ForumThread**: Individual thread display with replies and interactions
- **ForumPostForm**: Post creation and editing interface
- **ForumPostList**: Paginated post listings with sorting options
- **Type Definitions**: Comprehensive TypeScript types in `src/types/forum.ts`

### Forum Features
- **Voting System**: Upvote/downvote for posts and replies with user vote tracking
- **Hot and Pinned Posts**: Content promotion system for important discussions
- **Category System**: Organize discussions by topics and game categories
- **Real-time Updates**: Live vote counts and reply notifications
- **Moderation Tools**: Admin controls for content management and user moderation

## Real-time Features

### Analytics and Tracking
- **View Tracking**: Unique daily view tracking via `review_view_tracking` table
- **Real-time Updates**: Supabase subscriptions for live data
- **Anonymous Support**: IP-based tracking with session fallbacks
- **Performance Monitoring**: Live analytics dashboard for admin users

### Content Monetization
- **Sponsor System**: Banner and content monetization in `src/components/dashboard/`
- **Analytics Tracking**: Sponsor performance and engagement metrics
- **Ad Management**: Placeholder and actual ad integration
- **Revenue Analytics**: Creator earnings and performance tracking

## Admin System Architecture

### Multi-Factor Authentication (MFA)
- **TOTP Implementation**: Time-based one-time passwords with QR codes
- **MFA Management**: Setup, verification, and recovery in `src/lib/security/mfa.ts`
- **Admin Security**: MFA required for admin access with hierarchical permissions
- **Audit Logging**: Comprehensive MFA event tracking

### Security Implementation
- **Rate Limiting**: Built-in throttling on critical operations
- **Hierarchical Permissions**: Multi-level admin access controls
- **Audit Trail**: Complete admin action logging in `audit_logs` table
- **User Suspension**: Sophisticated suspension system with reason tracking
- **Session Management**: Secure session handling with automatic timeouts

### Admin Settings Implementation
- **Settings Management**: Comprehensive admin settings in `src/lib/admin/settings-*` files
- **Schema Validation**: Zod schemas with Portuguese error messages
- **Server Actions**: Form handling with authentication checks
- **Categories**: General, SEO, Content, Security, Notifications, and Integrations settings
- **Service Layer**: Business logic in `src/lib/admin/settingsService.ts`

### Admin Page Structure
- **Main Admin**: `/admin` - Overview dashboard with system stats
- **User Management**: `/admin/users` - User list, editing, suspension controls
- **Review Management**: `/admin/reviews` - Review moderation and management
- **Tags Management**: `/admin/tags` - Tag system management and analytics
- **Settings**: `/admin/settings` - System configuration with category tabs
- **Analytics**: `/admin/analytics` - Real-time analytics and monitoring
- **Security**: `/admin/security` - MFA setup, audit logs, and security monitoring
- **Moderation**: `/admin/moderation` - Content moderation tools
- **Ads Management**: `/admin/ads` - Advertisement and monetization management

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.