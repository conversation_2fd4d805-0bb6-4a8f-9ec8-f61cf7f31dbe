import React from 'react';
import GamePricesDemo from '@/components/game/GamePricesDemo';
import Head from 'next/head';

export default function DemoPricesPage() {
  return (
    <>
      <Head>
        <title>Demo: Widget de Preços Interativo | CriticalPixel</title>
        <meta name="description" content="Demonstração do novo widget de preços interativo com MagicUI" />
      </Head>
      
      <main className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-950 dark:to-slate-900">
        <div className="container mx-auto py-8">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              🎮 Widget de Preços Interativo
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Experiência redesenhada com MagicUI para descoberta de ofertas especiais
            </p>
          </div>
          
          <GamePricesDemo />
        </div>
      </main>
    </>
  );
} 