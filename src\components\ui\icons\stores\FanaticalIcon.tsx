import React from 'react';

interface IconProps {
  className?: string;
  [key: string]: any;
}

const FanaticalIcon: React.FC<IconProps> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 1024 1024"
    {...props}
  >
    <title>Fanatical</title>
    <circle cx="512" cy="512" r="512" fill="transparent"/>
    <path d="M259.8 468.5h364.4c1.5 0 2.9-.9 3.5-2.2l33.1-71.7c.9-1.9.1-4.2-1.9-5.1-.5-.2-1.1-.4-1.6-.4H287.5C331 309.8 415.2 256 512 256c141.3 0 255.8 114.6 255.8 256S653.3 768 512 768 256.2 653.4 256.2 512c0-14.8 1.2-29.4 3.6-43.5zm157.5 207.3c0 2.8 2.3 5.1 5.1 5.1h6.6l80.4-101.1h63.3c1.5 0 2.9-.9 3.5-2.2l33.1-71.6c.2-.5.4-1.1.4-1.6 0-2.1-1.7-3.8-3.8-3.8H417.3v175.2z" fill="currentColor"/>
  </svg>
);

export default FanaticalIcon;