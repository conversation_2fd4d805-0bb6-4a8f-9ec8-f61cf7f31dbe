# 🎮 G2A Affiliate Program & Web Scraping Integration Guide

## 📋 **Implementation Status**

### 🚧 **G2A INTEGRATION - READY FOR IMPLEMENTATION**
- **Status**: 📋 **IMPLEMENTATION GUIDE COMPLETE**
- **Version**: 1.0 Complete Research & Planning
- **Date**: January 31, 2025

### 🎯 **What Needs to be Implemented:**
- ⏳ **G2A Web Scraping Service** - Custom scraping solution (no public API available)
- ⏳ **G2A Goldmine Affiliate Integration** - Affiliate link generation with tracking
- ⏳ **Price Scraping Integration** - G2A added to main price scraping system
- ✅ **Database Support** - `affiliate_url` column already exists in `game_prices` table
- ⏳ **UI Components** - G2A branding and affiliate link detection in widgets
- ⏳ **Environment Configuration** - Ready for affiliate credentials
- ⏳ **Error Handling** - Graceful fallbacks when scraping fails

---

## 🔍 **Research Summary**

### **G2A API Situation**
❌ **No Public Price API**: G2A does not offer a public API for price fetching like Kinguin
✅ **Seller API Only**: G2A's API is designed for sellers to import/export products
🌐 **Web Scraping Required**: Price data must be obtained through web scraping

### **G2A Goldmine Affiliate Program**
✅ **Up to 5% Commission**: Depending on item category
✅ **24-Hour Cookie Duration**: Reactivated on each visit
✅ **€1-€2 for G2A Plus**: Additional revenue from membership referrals
✅ **Easy Payout**: Bank account or G2A Marketplace spending

---

## 🔐 **Getting Your G2A Goldmine Credentials**

### **Step 1: Register for G2A Goldmine**
1. **Visit**: [https://www.g2a.com/goldmine/](https://www.g2a.com/goldmine/)
2. **Sign up** for a G2A account (free)
3. **Access Goldmine Dashboard**: [https://dashboard.g2a.com/goldmine/overview](https://dashboard.g2a.com/goldmine/overview)
4. **No approval required** - instant access to affiliate program

### **Step 2: Get Your Affiliate ID**
Once logged in to Goldmine dashboard:
- **Affiliate ID**: Found in your dashboard settings
- **Commission Structure**: Up to 5% for digital items, €1-€2 for G2A Plus
- **Cookie Duration**: 24 hours (reactivated on each visit)
- **Payment Methods**: Bank transfer, G2A Marketplace wallet

### **Step 3: Generate Referral Links**
1. **Product Links**: Create referral links for specific games
2. **General Links**: Create links to G2A homepage with your tracking
3. **Custom UTM Parameters**: Add tracking for analytics

---

## ⚙️ **Configuration Setup**

### **Environment Variables**
Update your `.env.local` file with your credentials:

```env
# G2A Goldmine Configuration
G2A_AFFILIATE_ID=your_actual_affiliate_id_here
G2A_GOLDMINE_ENABLED=true
G2A_SCRAPING_ENABLED=true
G2A_SCRAPING_DELAY=2000  # Delay between requests in ms
```

### **Testing Configuration**
For testing, use development mode:
```env
G2A_SCRAPING_ENABLED=false  # Disable during development
```

---

## 🚀 **Implementation Architecture**

### **1. G2A Web Scraping Service**
```typescript
// New service to be created: g2aScrapingService.ts
import { PriceData } from '@/types/price';

export class G2AScrapingService {
  private affiliateId: string;
  private baseUrl: string;
  private scrapingEnabled: boolean;

  constructor() {
    this.affiliateId = process.env.G2A_AFFILIATE_ID || '';
    this.baseUrl = 'https://www.g2a.com';
    this.scrapingEnabled = process.env.G2A_SCRAPING_ENABLED === 'true';
  }

  // Search for game on G2A
  async searchGame(gameName: string): Promise<G2AGameResult[]>
  
  // Get price for specific game
  async getGamePrice(gameName: string, region: string): Promise<PriceData | null>
  
  // Generate affiliate URL with tracking
  generateAffiliateUrl(productUrl: string, gameName?: string): string
}
```

### **2. Affiliate Link Generation**
```typescript
// Automatically generates G2A Goldmine affiliate URLs
const affiliateUrl = g2aScrapingService.generateAffiliateUrl(productUrl, gameName);
// Result: https://www.g2a.com/product-page?reflink=YOUR_AFFILIATE_ID&utm_source=criticalpixel
```

### **3. Database Storage**
```sql
-- Prices stored with affiliate URLs (existing structure)
INSERT INTO game_prices (
  game_id, store_name, price, store_url, affiliate_url, currency, region_code
) VALUES (
  'game-123', 'G2A', '$19.99', 'https://g2a.com/game/xyz', 
  'https://g2a.com/game/xyz?reflink=affiliate_id', 'USD', 'us'
);
```

### **4. Frontend Display**
- 🔗 **Affiliate Links**: Automatically used when available
- 💰 **Visual Indicator**: "Link de afiliado" label for G2A
- 🎨 **Branding**: Blue/orange color scheme matching G2A brand
- 📱 **Responsive**: Works on all device sizes

---

## 🛠️ **Technical Implementation Steps**

### **Phase 1: Core Service Creation**
- [ ] **Create g2aScrapingService.ts** - Main scraping service
- [ ] **Implement search functionality** - Game search on G2A
- [ ] **Add price extraction** - Parse price data from search results
- [ ] **Implement affiliate URL generation** - Goldmine link creation
- [ ] **Add error handling** - Graceful failures and retries

### **Phase 2: Integration**
- [ ] **Update priceScrapingService.ts** - Add G2A to main service
- [ ] **Add G2A to store configurations** - Include in supported stores
- [ ] **Update GamePricesWidget.tsx** - Add G2A branding and styling
- [ ] **Add G2A currency support** - Handle multiple currencies
- [ ] **Implement rate limiting** - Respect G2A's servers

### **Phase 3: UI Enhancement**
- [ ] **Add G2A store icon** - Create/import G2A logo component
- [ ] **Update store gradients** - Add G2A color scheme
- [ ] **Add affiliate indicators** - Visual cues for affiliate links
- [ ] **Test responsive design** - Ensure mobile compatibility

### **Phase 4: Testing & Optimization**
- [ ] **Test scraping reliability** - Verify price extraction accuracy
- [ ] **Test affiliate link generation** - Ensure tracking works
- [ ] **Performance optimization** - Minimize scraping impact
- [ ] **Error monitoring** - Log and handle failures gracefully

---

## 🌐 **Web Scraping Implementation Details**

### **G2A Search Strategy**
```typescript
// Search URL pattern
const searchUrl = `https://www.g2a.com/search?query=${encodeURIComponent(gameName)}`;

// Key elements to scrape:
// - Product title and URL
// - Current price
// - Original price (if discounted)
// - Availability status
// - Platform information
```

### **Rate Limiting & Ethics**
- **Request Delay**: 2-3 seconds between requests
- **User Agent**: Proper browser user agent
- **Respect robots.txt**: Follow G2A's scraping guidelines
- **Error Handling**: Graceful failures without overwhelming servers
- **Caching**: Cache results to minimize requests

### **Data Extraction Points**
```typescript
interface G2AGameResult {
  title: string;
  url: string;
  price: number;
  originalPrice?: number;
  currency: string;
  platform: string;
  availability: 'available' | 'out_of_stock';
  discountPercentage?: number;
}
```

---

## 💰 **G2A Goldmine Revenue System**

### **Commission Structure**
- **Digital Games**: Up to 5% commission (varies by category)
- **G2A Plus Membership**: €1 (1-month), €2 (12-month)
- **Cookie Duration**: 24 hours (reactivated on each visit)
- **Minimum Payout**: Varies by payment method

### **Link Tracking**
All affiliate links include:
```
?reflink=YOUR_AFFILIATE_ID&utm_source=criticalpixel&utm_medium=affiliate&utm_campaign=price_comparison
```

### **Performance Monitoring**
Track your performance via:
- **G2A Goldmine Dashboard**: Monitor clicks, conversions, earnings
- **Analytics Integration**: Custom tracking in CriticalPixel
- **Monthly Reports**: Automated payout calculations

---

## 🎨 **UI/UX Design Specifications**

### **G2A Branding**
- **Primary Color**: #FF6600 (G2A Orange)
- **Secondary Color**: #1E3A8A (G2A Blue)
- **Store Icon**: G2A logo (to be sourced)
- **Gradient**: `from-orange-600 to-blue-600`

### **Affiliate Link Styling**
```tsx
{price.affiliate_url && price.store_name === 'G2A' && (
  <span className="text-xs text-orange-300">
    💰 Link de afiliado
  </span>
)}
```

### **Button Text Variations**
- **With Affiliate**: "Comprar com desconto"
- **Without Affiliate**: "Ver na G2A"
- **With Discount**: "X% mais barato"

---

## 🔍 **Implementation Checklist**

### **Backend Implementation**
- [ ] Create `src/lib/services/g2aScrapingService.ts`
- [ ] Update `src/lib/services/priceScrapingService.ts`
- [ ] Add G2A to supported stores configuration
- [ ] Implement affiliate URL generation
- [ ] Add error handling and logging
- [ ] Test scraping functionality

### **Frontend Implementation**
- [ ] Update `src/components/game/GamePricesWidget.tsx`
- [ ] Update `src/components/game/GamePricesWidgetInteractive.tsx`
- [ ] Add G2A store icon component
- [ ] Update store gradients and styling
- [ ] Add affiliate link indicators
- [ ] Test responsive design

### **Configuration**
- [ ] Add environment variables to `.env.local`
- [ ] Configure G2A Goldmine affiliate ID
- [ ] Set up scraping parameters
- [ ] Configure rate limiting
- [ ] Test affiliate link generation

### **Testing**
- [ ] Test game search functionality
- [ ] Verify price extraction accuracy
- [ ] Test affiliate link generation
- [ ] Verify database storage
- [ ] Test UI components
- [ ] Performance testing

---

## ⚠️ **Important Considerations**

### **Legal & Ethical**
- **Terms of Service**: Review G2A's ToS regarding scraping
- **Rate Limiting**: Respect server resources
- **Data Usage**: Only extract necessary price information
- **Attribution**: Proper attribution to G2A

### **Technical Challenges**
- **Anti-Bot Protection**: G2A may have anti-scraping measures
- **Dynamic Content**: Prices may be loaded via JavaScript
- **Regional Variations**: Different prices for different regions
- **Currency Handling**: Multiple currencies and conversions

### **Maintenance**
- **Regular Updates**: G2A may change their website structure
- **Monitoring**: Track scraping success rates
- **Fallback Plans**: Handle when scraping fails
- **Performance**: Monitor impact on application performance

---

## 🎯 **Expected Results**

### **Game Page Integration**
- **Widget Location**: Both overview tab and sidebar
- **Store Display**: "G2A" with orange/blue branding
- **Price Format**: Proper currency formatting (€, $, £, etc.)
- **Availability**: Shows stock status
- **Discounts**: Displays percentage and original price

### **User Experience**
- **Visual Cue**: Orange "💰 Link de afiliado" text
- **Button Text**: "Comprar com desconto" for affiliate links
- **Tracking**: UTM parameters for analytics
- **Fallback**: Direct store links if affiliate not configured

---

## 📊 **Success Metrics**

### **Track These KPIs**
- **G2A Click-through Rate**: From price widget to G2A
- **Conversion Rate**: Clicks that result in purchases
- **Scraping Success Rate**: Percentage of successful price fetches
- **Monthly Commission**: Total affiliate earnings
- **User Satisfaction**: Feedback on price comparison accuracy

### **Monitoring Tools**
- **G2A Goldmine Dashboard**: Official affiliate tracking
- **Google Analytics**: Custom UTM tracking
- **Database Queries**: Price widget interaction logs
- **Error Monitoring**: Scraping failure rates

---

---

## 💻 **Detailed Code Implementation Examples**

### **G2A Scraping Service Template**
```typescript
// src/lib/services/g2aScrapingService.ts
import { PriceData } from '@/types/price';

interface G2AGameResult {
  title: string;
  url: string;
  price: number;
  originalPrice?: number;
  currency: string;
  platform: string;
  availability: 'available' | 'out_of_stock';
  discountPercentage?: number;
}

export class G2AScrapingService {
  private affiliateId: string;
  private baseUrl: string;
  private scrapingEnabled: boolean;
  private requestDelay: number;

  constructor() {
    this.affiliateId = process.env.G2A_AFFILIATE_ID || '';
    this.baseUrl = 'https://www.g2a.com';
    this.scrapingEnabled = process.env.G2A_SCRAPING_ENABLED === 'true';
    this.requestDelay = parseInt(process.env.G2A_SCRAPING_DELAY || '2000');
  }

  isConfigured(): boolean {
    return !!(this.affiliateId && this.scrapingEnabled);
  }

  async searchGame(gameName: string): Promise<G2AGameResult[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ G2A scraping not configured');
      return [];
    }

    try {
      const searchUrl = `${this.baseUrl}/search?query=${encodeURIComponent(gameName)}`;

      // Add delay to respect rate limiting
      await this.delay(this.requestDelay);

      const response = await fetch(searchUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
        }
      });

      if (!response.ok) {
        console.error(`❌ G2A search failed: ${response.status}`);
        return [];
      }

      const html = await response.text();
      return this.parseSearchResults(html);

    } catch (error) {
      console.error('❌ G2A scraping error:', error);
      return [];
    }
  }

  private parseSearchResults(html: string): G2AGameResult[] {
    // TODO: Implement HTML parsing logic
    // This will need to be implemented based on G2A's actual HTML structure
    // Use libraries like cheerio for server-side HTML parsing
    console.log('🔍 Parsing G2A search results...');
    return [];
  }

  generateAffiliateUrl(productUrl: string, gameName?: string): string {
    if (!this.affiliateId) {
      return productUrl;
    }

    try {
      const url = new URL(productUrl);
      url.searchParams.set('reflink', this.affiliateId);
      url.searchParams.set('utm_source', 'criticalpixel');
      url.searchParams.set('utm_medium', 'affiliate');
      url.searchParams.set('utm_campaign', 'price_comparison');

      if (gameName) {
        url.searchParams.set('utm_content', gameName.toLowerCase().replace(/\s+/g, '-'));
      }

      console.log(`🔗 Generated G2A affiliate URL: ${url.toString()}`);
      return url.toString();
    } catch (error) {
      console.warn('Failed to generate G2A affiliate URL:', error);
      return productUrl;
    }
  }

  async getGamePrice(gameName: string, region: string = 'global'): Promise<PriceData | null> {
    console.log(`🎮 G2A: Getting price for "${gameName}" in region ${region}`);

    const searchResults = await this.searchGame(gameName);
    if (!searchResults || searchResults.length === 0) {
      return null;
    }

    // Get the best match (first result for now)
    const bestMatch = searchResults[0];

    return {
      store_name: 'G2A',
      price: this.formatPrice(bestMatch.price, bestMatch.currency),
      original_price: bestMatch.originalPrice ?
        this.formatPrice(bestMatch.originalPrice, bestMatch.currency) : undefined,
      discount_percentage: bestMatch.discountPercentage,
      store_url: bestMatch.url,
      affiliate_url: this.generateAffiliateUrl(bestMatch.url, gameName),
      currency: bestMatch.currency,
      availability: bestMatch.availability,
      region_code: region
    };
  }

  private formatPrice(price: number, currency: string): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const g2aScrapingService = new G2AScrapingService();
```

### **Integration with Price Scraping Service**
```typescript
// Add to src/lib/services/priceScrapingService.ts

import { g2aScrapingService } from './g2aScrapingService';

// Add G2A to the scraping methods
async scrapeG2A(gameName: string, regionCode: string = DEFAULT_REGION): Promise<PriceData | null> {
  console.log(`🟠 G2A SCRAPER for "${gameName}" in region ${regionCode}`);

  try {
    if (!g2aScrapingService.isConfigured()) {
      console.error(`❌ G2A NOT CONFIGURED - MISSING AFFILIATE ID`);
      return null;
    }

    console.log(`✅ G2A configured, calling getGamePrice for region ${regionCode}...`);

    const g2aData = await g2aScrapingService.getGamePrice(gameName, regionCode);

    if (!g2aData) {
      console.error(`❌ G2A scraping returned null for "${gameName}" in region ${regionCode}`);
      return null;
    }

    console.log(`✅ G2A found price for region ${regionCode}: ${g2aData.currency} ${g2aData.price}`);

    return g2aData;

  } catch (error) {
    console.error(`💥 G2A SCRAPER ERROR for region ${regionCode}:`, error);
    return null;
  }
}

// Update the main updateGamePrices method to include G2A
async updateGamePrices(gameId: string, gameName: string, regionCode: string = DEFAULT_REGION, steamAppId?: string): Promise<boolean> {
  // ... existing code ...

  // Add G2A scraping
  console.log(`🟠 Fetching G2A prices for region ${regionCode}...`);
  const g2aPriceData = await this.scrapeG2A(gameName, regionCode);

  if (g2aPriceData) {
    const { error } = await supabase
      .from('game_prices')
      .upsert({
        game_id: gameId,
        store_name: 'G2A',
        price: g2aPriceData.price,
        original_price: g2aPriceData.original_price,
        discount_percentage: g2aPriceData.discount_percentage,
        store_url: g2aPriceData.store_url,
        affiliate_url: g2aPriceData.affiliate_url,
        currency: g2aPriceData.currency,
        availability: g2aPriceData.availability,
        region_code: regionCode,
        last_updated: new Date().toISOString()
      }, {
        onConflict: 'game_id,store_name,region_code'
      });

    if (error) {
      console.error(`❌ Error updating G2A price:`, error);
    } else {
      console.log(`✅ Updated G2A price: ${g2aPriceData.price} (${g2aPriceData.currency}) for region ${regionCode}`);
      hasAnyPrices = true;
    }
  }

  // ... rest of existing code ...
}
```

### **Frontend Component Updates**
```tsx
// Add to src/components/game/GamePricesWidget.tsx

// Add G2A to store gradients
const storeGradients = {
  'Steam': 'from-blue-600 to-blue-500',
  'Nuuvem': 'from-green-600 to-green-500',
  'Epic Games': 'from-gray-800 to-gray-700',
  'Instant Gaming': 'from-purple-600 to-purple-500',
  'Kinguin': 'from-orange-600 to-orange-500',
  'G2A': 'from-orange-600 to-blue-600', // G2A gradient
};

// Add G2A affiliate link detection
{price.affiliate_url && (price.store_name === 'Kinguin' || price.store_name === 'G2A') && (
  <span className="text-xs text-orange-300">
    💰 Link de afiliado
  </span>
)}
```

---

## 🔧 **Troubleshooting Guide**

### **Common Issues**

#### **"G2A scraping not configured"**
- **Solution**: Add `G2A_AFFILIATE_ID` to `.env.local`
- **Check**: Environment variables are loaded correctly
- **Verify**: `G2A_SCRAPING_ENABLED=true` is set

#### **"No results found for game"**
- **Solution**: Game might not be available on G2A
- **Check**: Try different game names or search terms
- **Debug**: Log the search URL and response

#### **Scraping fails with 403/429 errors**
- **Solution**: Increase delay between requests
- **Check**: `G2A_SCRAPING_DELAY` environment variable
- **Implement**: Better rate limiting and retry logic

#### **Affiliate links not working**
- **Solution**: Verify `G2A_AFFILIATE_ID` is set correctly
- **Check**: Links should contain `?reflink=YOUR_AFFILIATE_ID`
- **Test**: Click links and verify tracking in G2A dashboard

### **Debug Mode**
Enable detailed logging:
```env
NODE_ENV=development
G2A_SCRAPING_ENABLED=true
```

Look for console logs:
```
🟠 G2A: Getting price for "Game Name"
✅ G2A found price $19.99
❌ G2A scraping failed
```

---

## 🚀 **Go Live Checklist**

### **Before Production**
- [ ] **Affiliate ID**: Added to production environment variables
- [ ] **Scraping Configuration**: Tested and optimized
- [ ] **Rate Limiting**: Configured appropriately
- [ ] **Error Handling**: Comprehensive error management
- [ ] **Affiliate Links**: Verified with tracking parameters

### **After Production**
- [ ] **Monitor Performance**: Check G2A Goldmine dashboard for activity
- [ ] **Track Conversions**: Monitor affiliate earnings
- [ ] **Scraping Health**: Monitor success rates and errors
- [ ] **User Feedback**: Ensure smooth user experience
- [ ] **Error Monitoring**: Watch for scraping issues

---

## 💡 **Business Benefits**

### **Revenue Generation**
- **Passive Income**: Earn up to 5% commission on every G2A sale
- **24-Hour Tracking**: Continue earning from returning customers
- **Global Reach**: G2A available in multiple regions
- **High Conversion**: Competitive prices attract buyers

### **User Value**
- **More Options**: Additional store for price comparison
- **Better Deals**: G2A often has competitive digital game prices
- **Instant Delivery**: Digital keys delivered immediately
- **Regional Pricing**: Supports multiple currencies

---

## 📈 **Expected Impact**

### **Price Coverage**
- **+25% More Games**: G2A has extensive digital game catalog
- **Better Prices**: Often 15-40% below retail
- **Regional Support**: Multiple currencies and regions
- **Instant Availability**: Digital keys always in stock

### **Revenue Potential**
- **Conservative Estimate**: 10-20 G2A clicks per game page
- **Conversion Rate**: 2-5% (industry standard for price comparison)
- **Average Order**: €15-50 per digital game
- **Monthly Revenue**: Depends on traffic and conversion

---

*Last Updated: January 31, 2025*
*Status: 📋 Ready for Implementation*
